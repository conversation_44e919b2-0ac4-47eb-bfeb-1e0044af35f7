import React from 'react'

const BeforeAfter = () => {
  return (
    <section className=' bg-[#212121]'>
      <div className='relative grid grid-cols-[12px_auto_12px] md:grid-cols-[40px_auto_40px] lg:grid-cols-[40px_auto_40px] grid-rows-[82px_82px_auto] md:grid-rows-[300px_300px_auto] lg:grid-rows-[300px_300px_auto] border-none'>
        <div className='flex flex-col items-center justify-center'></div>
        <div className='flex flex-col items-center justify-center row-span-2 bg-white rounded-t-3xl h-full'>
          <div className='inside-img rounded-2xl self-end w-full h-full bg-[#212121]'>
            {/* Desktop/Tablet Image */}
            <img
              src='/images/BeforeAfter.png'
              alt='Before and After Remediation'
              className='w-full h-full hidden sm:block'
            />
            {/* Mobile Image */}
            <img
              src='/images/small-beforeAfter.png'
              alt='Before and After Remediation'
              className='w-full h-full block sm:hidden'
            />
          </div>
        </div>
        <div className='flex flex-col items-center justify-center'></div>
        <div className='flex flex-col items-center justify-center bg-white rounded-tr-3xl'></div>
        <div className='flex flex-col items-center justify-center bg-white rounded-tl-3xl'></div>
        <div className='flex flex-col items-center justify-center col-span-3 bg-white'>
          6
        </div>
      </div>
    </section>
  )
}

export default BeforeAfter
