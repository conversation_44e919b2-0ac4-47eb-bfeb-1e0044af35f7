import React from 'react'
import { FaFacebookF, FaWhatsapp, FaInstagram } from 'react-icons/fa'
import { FaXTwitter } from 'react-icons/fa6'

const Footer = () => {
  return (
    <footer
      className='text-gray-400 font-satoshi'
      style={{
        backgroundColor: 'black',
        backgroundImage: "url('/images/Footer-background.png')",
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
      }}
    >
      <div className='container-custom pt-12 md:pt-16 lg:pt-20 pb-10 md:pb-12 px-4 md:px-6 lg:px-10'>
        {/* Main Footer Content */}
        <div className='flex flex-col md:flex-row justify-between items-start gap-8 md:gap-12'>
          {/* Column 1: Logo and Address */}
          <div className='flex-shrink-0 md:w-1/4 w-full'>
            <a href='#home'>
              <img
                src='/images/Logo(Horizontal Variation).svg'
                alt='PureCleanUp Services'
                className='h-12 md:h-16 lg:h-20 mb-4 md:mb-6'
              />
            </a>
            <p className='text-sm md:text-base lg:text-lg text-gray-300 leading-relaxed'>
              5775 Sleman Sembada, amikom 999
              <br />
              Madboutdat, CO 89999
              <br />
              (404) 289-8899
            </p>
          </div>

          {/* Column 2: Navigation */}
          <div className='flex-grow flex justify-start md:justify-center items-start w-full md:w-auto'>
            <div className='flex flex-col md:flex-row space-y-3 md:space-y-0 md:space-x-8 lg:space-x-12 w-full md:w-auto'>
              <a
                href='#home'
                className='text-base md:text-base lg:text-lg hover:text-white transition-colors duration-200 block'
              >
                HOME
              </a>
              <a
                href='#about'
                className='text-base md:text-base lg:text-lg hover:text-white transition-colors duration-200 block'
              >
                ABOUT US
              </a>
              <a
                href='#services'
                className='text-base md:text-base lg:text-lg hover:text-white transition-colors duration-200 block'
              >
                OUR SERVICES
              </a>
              <a
                href='#testimonials'
                className='text-base md:text-base lg:text-lg hover:text-white transition-colors duration-200 block'
              >
                TESTIMONIALS
              </a>
            </div>
          </div>

          {/* Column 3: Newsletter */}
          <div className='w-full md:w-1/3 text-left md:text-right'>
            <h3 className='font-medium text-white text-lg md:text-xl lg:text-2xl mb-3 md:mb-4 leading-tight'>
              Enter your email to get our newsletter
            </h3>
            <p className='text-sm md:text-base lg:text-lg text-gray-300 mb-4 md:mb-6 leading-relaxed'>
              Use correct email address so as not to miss out.
            </p>
            <form className='flex w-full justify-start md:justify-end mb-2'>
              <div className='flex w-full md:w-auto items-center bg-white rounded-lg p-1 border border-gray-600 focus-within:border-green-500'>
                <input
                  type='email'
                  placeholder='Enter your email address'
                  className='flex-grow bg-transparent text-gray-800 px-3 md:px-4 py-2 md:py-3 focus:outline-none text-sm md:text-base w-full min-w-0'
                />
                <button
                  type='submit'
                  className='bg-green-600 text-white font-semibold px-4 md:px-6 py-2 md:py-3 rounded-md hover:bg-green-700 text-sm md:text-base flex-shrink-0 transition-colors duration-200'
                >
                  Send
                </button>
              </div>
            </form>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className='mt-12 md:mt-16 lg:mt-20 relative px-2 md:px-4'>
          <div className='relative border-t rounded-lg px-4 md:px-6 py-4 md:py-6 border-gray-600 pt-4 md:pt-6 flex flex-col md:flex-row justify-between items-center text-xs md:text-sm lg:text-base text-gray-500 gap-y-4 md:gap-y-2'>
            <p className='flex-shrink-0 text-sm md:text-base lg:text-lg text-center md:text-left'>
              ALL RIGHTS RESERVED. COPYRIGHTS © 2025 LTD.
            </p>
            <div className='flex flex-col items-center space-y-3 md:space-y-2'>
              <div className='flex items-center space-x-4 md:space-x-6'>
                <a
                  href='#'
                  className='hover:text-white text-sm md:text-base lg:text-lg transition-colors duration-200'
                >
                  PRIVACY POLICY
                </a>
                <a
                  href='#'
                  className='hover:text-white text-sm md:text-base lg:text-lg transition-colors duration-200'
                >
                  TERMS & CONDITIONS
                </a>
              </div>
              <div className='flex items-center space-x-4 md:space-x-6 flex-shrink-0'>
                <a
                  href='#'
                  className='hover:text-white transition-colors duration-200'
                >
                  <FaFacebookF className='w-5 h-5 md:w-5 md:h-5 lg:w-6 lg:h-6' />
                </a>
                <a
                  href='#'
                  className='hover:text-white transition-colors duration-200'
                >
                  <FaWhatsapp className='w-5 h-5 md:w-5 md:h-5 lg:w-6 lg:h-6' />
                </a>
                <a
                  href='#'
                  className='hover:text-white transition-colors duration-200'
                >
                  <FaInstagram className='w-5 h-5 md:w-5 md:h-5 lg:w-6 lg:h-6' />
                </a>
                <a
                  href='#'
                  className='hover:text-white transition-colors duration-200'
                >
                  <FaXTwitter className='w-5 h-5 md:w-5 md:h-5 lg:w-6 lg:h-6' />
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
