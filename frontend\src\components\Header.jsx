import React, { useState, useRef, useEffect } from 'react'
import { Link, useLocation } from 'react-router-dom'
import {
  Bars3Icon,
  XMarkIcon,
  ChevronDownIcon,
  ChevronRightIcon,
} from '@heroicons/react/24/outline'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isServicesMenuOpen, setIsServicesMenuOpen] = useState(false)
  const [activeSection, setActiveSection] = useState('home')
  const servicesMenuTimeoutRef = useRef(null)

  const navigation = [
    { name: 'Home', href: '/', isRoute: true },
    { name: 'About us', href: '#about', isRoute: false },
    { name: 'Testimonials', href: '#testimonials', isRoute: false },
    { name: 'Contact us', href: '#contact', isRoute: false },
  ]

  const services = [
    {
      name: 'Water and Flood Damage Restoration',
      path: '/services/water-flood-restoration',
    },
    {
      name: 'Mold Inspection and Testing',
      path: '/services/mold-inspection-testing',
    },
    { name: 'Mold Remediation', path: '/services/mold-remediation' },
    {
      name: 'New Construction Mold Remediation',
      path: '/services/new-construction-mold',
    },
    {
      name: 'Drying, Remediation and Restoration Equipment Rentals',
      path: '/services/equipment-rentals',
    },
    { name: 'Odour Removal and Deodorize', path: '/services/odour-removal' },
    {
      name: 'Indoor Air Quality Testing',
      path: '/services/indoor-air-quality',
    },
  ]

  useEffect(() => {
    const handleScroll = () => {
      const sections = ['home', 'about', 'services', 'testimonials', 'contact']
      const scrollPosition = window.scrollY + 100 // Add offset

      for (const sectionId of sections) {
        const section = document.getElementById(sectionId)
        if (section) {
          const sectionTop = section.offsetTop
          const sectionHeight = section.offsetHeight
          if (
            scrollPosition >= sectionTop &&
            scrollPosition < sectionTop + sectionHeight
          ) {
            setActiveSection(sectionId)
            break
          }
        }
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const handleServicesMenuEnter = () => {
    clearTimeout(servicesMenuTimeoutRef.current)
    setIsServicesMenuOpen(true)
  }

  const handleServicesMenuLeave = () => {
    servicesMenuTimeoutRef.current = setTimeout(() => {
      setIsServicesMenuOpen(false)
    }, 200)
  }

  return (
    <header className='bg-white shadow-md fixed w-full top-0 z-50 h-[80px] border-b-2 border-yellow-500'>
      <div className='container-custom h-full w-full '>
        <div className='flex justify-between items-center h-full w-full'>
          <div className='flex-shrink-0'>
            <Link to='/'>
              <img
                className='w-auto'
                style={{ width: '210.36px', height: '55px' }}
                src='/images/Logo(Horizontal Variation).svg'
                alt='PureCleanUp Services'
              />
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className='hidden md:flex flex-grow justify-center items-center'>
            <div className='flex items-center space-x-8'>
              {navigation.slice(0, 2).map((item) =>
                item.isRoute ? (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`nav-link ${
                      window.location.pathname === item.href
                        ? 'text-green-600'
                        : 'text-gray-800 hover:text-green-600'
                    }`}
                  >
                    {item.name}
                  </Link>
                ) : (
                  <a
                    key={item.name}
                    href={item.href}
                    className={`nav-link ${
                      activeSection === item.href.substring(1)
                        ? 'text-green-600'
                        : 'text-gray-800 hover:text-green-600'
                    }`}
                  >
                    {item.name}
                  </a>
                )
              )}
              <div
                className='relative'
                onMouseEnter={handleServicesMenuEnter}
                onMouseLeave={handleServicesMenuLeave}
              >
                <button
                  className={`flex items-center nav-link ${
                    activeSection === 'services'
                      ? 'text-green-600'
                      : 'text-gray-800 hover:text-green-600'
                  }`}
                >
                  <span>Services</span>
                  <ChevronDownIcon
                    className={`w-4 h-4 ml-2 transition-transform ${
                      isServicesMenuOpen ? 'transform rotate-180' : ''
                    }`}
                  />
                </button>
                {isServicesMenuOpen && (
                  <div
                    className='absolute right-0 top-full mt-4 bg-white rounded-lg shadow-lg p-3 ring-1 ring-black ring-opacity-5'
                    style={{ width: '412px', minHeight: '497px' }}
                  >
                    {services.map((service) => (
                      <Link
                        key={service.name}
                        to={service.path}
                        className='services-dropdown-item flex justify-between items-center px-3 py-2.5 mb-1 text-gray-700 bg-white hover:bg-gray-50 hover:text-gray-900 border border-gray-100 rounded-md transition-colors duration-200'
                        onClick={() => setIsServicesMenuOpen(false)}
                      >
                        <span>{service.name}</span>
                        <ChevronRightIcon className='h-4 w-4 text-gray-400' />
                      </Link>
                    ))}
                  </div>
                )}
              </div>
              {navigation.slice(2).map((item) =>
                item.isRoute ? (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`nav-link ${
                      window.location.pathname === item.href
                        ? 'text-green-600'
                        : 'text-gray-800 hover:text-green-600'
                    }`}
                  >
                    {item.name}
                  </Link>
                ) : (
                  <a
                    key={item.name}
                    href={item.href}
                    className={`nav-link ${
                      activeSection === item.href.substring(1)
                        ? 'text-green-600'
                        : 'text-gray-800 hover:text-green-600'
                    }`}
                  >
                    {item.name}
                  </a>
                )
              )}
            </div>
          </nav>

          <div className='hidden md:flex items-center'>
            <img
              src='/images/PhoneCall1.png'
              alt='Phone call icon'
              className='h-8 w-8'
            />
            <div className='ml-3'>
              <p className='font-body text-xs text-[#999999] font-normal'>
                Customer Services
              </p>
              <a
                href='tel:************'
                className='text-sm font-bold text-gray-800'
              >
                ************
              </a>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className='md:hidden'>
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className='p-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100'
            >
              {isMenuOpen ? (
                <XMarkIcon className='h-6 w-6' />
              ) : (
                <Bars3Icon className='h-6 w-6' />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className='md:hidden bg-white shadow-lg'>
          <div className='px-2 pt-2 pb-4 space-y-1'>
            {navigation.map((item) =>
              item.isRoute ? (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`block px-3 py-2 text-base font-medium text-center rounded-md ${
                    window.location.pathname === item.href
                      ? 'text-green-600 bg-green-50'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ) : (
                <a
                  key={item.name}
                  href={item.href}
                  className={`block px-3 py-2 text-base font-medium text-center rounded-md ${
                    item.name === 'Home'
                      ? 'text-green-600 bg-green-50'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </a>
              )
            )}
            <div className='border-t pt-2 mt-2'>
              <p className='px-3 py-2 text-base font-medium text-center text-gray-500'>
                Services
              </p>
              {services.map((service) => (
                <Link
                  key={service.name}
                  to={service.path}
                  className='block px-3 py-2 text-base text-center text-gray-700 hover:bg-gray-50 rounded-md'
                  onClick={() => setIsMenuOpen(false)}
                >
                  {service.name}
                </Link>
              ))}
            </div>
          </div>
        </div>
      )}
    </header>
  )
}

export default Header
