import React from 'react'
import ServicePageTemplate from '../components/ServicePageTemplate'

const NewConstructionMold = () => {
  const pageData = {
    heroTitle: "New Construction Mold Remediation",
    heroSubtitle: "Specialized mold services for new construction projects",
    heroImages: [
      {
        src: "/images/construction-hero-1.jpg",
        alt: "New construction mold remediation"
      },
      {
        src: "/images/construction-hero-2.jpg", 
        alt: "Construction site mold prevention"
      },
      {
        src: "/images/construction-hero-3.jpg",
        alt: "Building materials protection"
      }
    ],
    serviceTitle: "New Construction Mold Remediation",
    serviceDescription: "New construction projects are particularly vulnerable to mold growth due to moisture exposure during building. Our specialized team provides comprehensive mold prevention and remediation services specifically designed for construction sites and newly built properties.",
    serviceFeatures: [
      "Pre-construction moisture assessment",
      "Building material protection strategies",
      "Construction site mold monitoring", 
      "Rapid response to water intrusion",
      "Specialized drying for new materials",
      "Air quality control during construction",
      "Final clearance testing and certification",
      "Builder and contractor coordination"
    ],
    focusAreas: {
      residential: [
        "New home construction protection",
        "Custom home mold prevention",
        "Renovation project oversight",
        "Addition and extension services",
        "Basement finishing protection",
        "Moisture barrier installation",
        "HVAC system protection",
        "Final inspection services"
      ],
      commercial: [
        "Commercial building construction",
        "Multi-family development projects",
        "Office building construction",
        "Retail space development",
        "Industrial facility construction",
        "Institutional building projects",
        "Large-scale development oversight",
        "Contractor training and support"
      ]
    },
    commonSigns: {
      title: "When new construction needs mold services:",
      signs: [
        "Water intrusion during construction",
        "Extended exposure to weather",
        "Improper material storage",
        "Inadequate ventilation during building",
        "Delayed drying after water exposure",
        "Visible mold on new materials"
      ]
    },
    additionalImages: [
      {
        src: "/images/construction-protection.jpg",
        alt: "Construction material protection"
      },
      {
        src: "/images/site-monitoring.jpg", 
        alt: "Construction site monitoring"
      },
      {
        src: "/images/material-drying.jpg",
        alt: "Specialized material drying"
      },
      {
        src: "/images/clearance-testing.jpg",
        alt: "Final clearance testing"
      }
    ]
  }

  return <ServicePageTemplate {...pageData} />
}

export default NewConstructionMold
