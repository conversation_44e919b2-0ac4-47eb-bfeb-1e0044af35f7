# Pure Clean Services - Frontend

A modern, responsive website for Pure Clean Services built with React.js, Tailwind CSS, and Vite.

## Features

- **Modern Design**: Clean, professional design with beautiful gradients and animations
- **Responsive Layout**: Fully responsive design that works on all devices
- **Component Architecture**: Well-structured React components for easy maintenance
- **Tailwind CSS**: Utility-first CSS framework for rapid styling
- **Performance Optimized**: Built with Vite for fast development and optimized builds
- **Accessibility**: Semantic HTML and proper ARIA labels for better accessibility

## Sections

1. **Header**: Navigation with logo and menu
2. **Hero**: Eye-catching landing section with call-to-action
3. **Services**: Comprehensive list of cleaning services offered
4. **About**: Company information and values
5. **Why Choose Us**: Key differentiators and benefits
6. **Testimonials**: Customer reviews and ratings
7. **Contact**: Contact form and business information
8. **Footer**: Additional links and company details

## Technologies Used

- **React.js**: Frontend framework
- **Vite**: Build tool and development server
- **Tailwind CSS**: Utility-first CSS framework
- **Heroicons**: Beautiful hand-crafted SVG icons
- **Unsplash**: High-quality images

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Navigate to the frontend directory:

   ```bash
   cd frontend
   ```

2. Install dependencies:

   ```bash
   npm install
   ```

3. Start the development server:

   ```bash
   npm run dev
   ```

4. Open your browser and visit `http://localhost:5173`

### Build for Production

To create a production build:

```bash
npm run build
```

The built files will be in the `dist` directory.

## Project Structure

```
frontend/
├── src/
│   ├── components/
│   │   ├── Header.jsx
│   │   ├── Hero.jsx
│   │   ├── Services.jsx
│   │   ├── About.jsx
│   │   ├── WhyChooseUs.jsx
│   │   ├── Testimonials.jsx
│   │   ├── Contact.jsx
│   │   └── Footer.jsx
│   ├── App.jsx
│   ├── App.css
│   ├── index.css
│   └── main.jsx
├── public/
├── package.json
├── tailwind.config.js
├── postcss.config.js
└── vite.config.js
```

## Customization

### Colors

The color scheme can be customized in `tailwind.config.js`:

```javascript
colors: {
  primary: {
    // Custom primary colors
  },
  secondary: {
    // Custom secondary colors
  }
}
```

### Content

Update the content in each component file to match your business information:

- Company name and logo
- Services offered
- Contact information
- Testimonials
- Images

### Styling

Additional custom styles can be added in `src/index.css` using Tailwind's `@layer` directive.

## License

This project is licensed under the MIT License.+ Vite

This template provides a minimal setup to get React working in Vite with HMR and some ESLint rules.

Currently, two official plugins are available:

- [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react) uses [Babel](https://babeljs.io/) for Fast Refresh
- [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react-swc) uses [SWC](https://swc.rs/) for Fast Refresh

## Expanding the ESLint configuration

If you are developing a production application, we recommend using TypeScript with type-aware lint rules enabled. Check out the [TS template](https://github.com/vitejs/vite/tree/main/packages/create-vite/template-react-ts) for information on how to integrate TypeScript and [`typescript-eslint`](https://typescript-eslint.io) in your project.
