import React from 'react'
import { ArrowRightIcon } from '@heroicons/react/24/solid'

const CTA = () => {
  return (
    <section className='bg-[#212121] py-1 lg:py-2'>
      <div className='container-custom'>
        <div className='flex flex-col lg:flex-row gap-4 lg:gap-20 items-start'>
          {/* Image section */}
          <div className='relative p-10 w-full lg:w-[50%] h-[500px] lg:h-[700px] bg-red-500'>
            {/* Main background image - person cleaning */}
            <div className=' w-[280px] h-[240px] lg:w-[633px] lg:h-[560px]'>
              <img
                src='/images/Rectangle.png'
                alt='Professional cleaning service'
                className='w-full h-full rounded-2xl object-cover blur-[1px]'
              />
            </div>

            {/* Top left card - cleaning equipment */}
            <div className='absolute top-5 left-4 lg:top-5 lg:left-2 w-[125px] h-[119px] lg:w-[265px] lg:h-[252px] bg-white rounded-2xl p-1 shadow-2xl'>
              <div className='w-full h-full bg-gray-100 rounded-xl overflow-hidden'>
                <img
                  src='/images/top-left.png'
                  alt='Professional cleaning equipment'
                  className='w-full h-full object-cover'
                />
              </div>
            </div>

            {/* Bottom right card - building exterior with service van */}
            <div className='absolute bottom-[150px] right-4 lg:bottom-6 lg:right-0 w-[160px] h-[150px] lg:w-[396px] lg:h-[377px] p-1 shadow-2xl'>
              <div className='w-full h-full bg-gray-100 rounded-xl overflow-hidden'>
                <img
                  src='/images/Vector.png'
                  alt='Property restoration services'
                  className='w-full h-full object-cover'
                />
              </div>
            </div>
          </div>

          {/* Content section */}
          <div className='space-y-4 lg:space-y-10 w-full lg:w-[45%] pt-6 lg:pt-10 px-4 lg:px-0 relative'>
            <h2 className='text-2xl lg:text-5xl xl:text-6xl font-semibold text-white leading-tight'>
              Leverage our expertise to restore your property to its original
              condition
            </h2>
            <p className='text-gray-300 text-sm lg:text-xl leading-relaxed max-w-lg'>
              With certified professionals and eco-friendly solutions, we bring
              safety and cleanliness back to your home or business. Trust us to
              handle your restoration needs with care and expertise.
            </p>
            <div className='pt-4'>
              <a
                href='#contact'
                className='inline-flex items-center justify-center bg-[#009D22] hover:bg-green-600 text-white font-semibold py-3 px-6 lg:py-2.5 lg:px-3 rounded-md transition-all duration-200 text-sm shadow-lg hover:shadow-xl transform hover:-translate-y-1'
              >
                Contact us Today ↗
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default CTA
