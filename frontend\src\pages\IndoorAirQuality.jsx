import React from 'react'
import ServicePageTemplate from '../components/ServicePageTemplate'

const IndoorAirQuality = () => {
  const pageData = {
    heroTitle: "Indoor Air Quality Testing",
    heroSubtitle: "Comprehensive air quality assessment and improvement",
    heroImages: [
      {
        src: "/images/air-quality-hero-1.jpg",
        alt: "Indoor air quality testing equipment"
      },
      {
        src: "/images/air-quality-hero-2.jpg", 
        alt: "Professional air quality assessment"
      },
      {
        src: "/images/air-quality-hero-3.jpg",
        alt: "Clean, healthy indoor environment"
      }
    ],
    serviceTitle: "Indoor Air Quality Testing",
    serviceDescription: "Poor indoor air quality can affect your health, comfort, and productivity. Our certified specialists conduct comprehensive air quality assessments to identify pollutants, allergens, and other contaminants, providing detailed reports and recommendations for improvement.",
    serviceFeatures: [
      "Comprehensive air quality assessment",
      "Mold and allergen testing",
      "Chemical and VOC detection", 
      "Particulate matter analysis",
      "Humidity and ventilation evaluation",
      "HVAC system performance testing",
      "Detailed reporting and recommendations",
      "Follow-up testing and monitoring"
    ],
    focusAreas: {
      residential: [
        "Home air quality assessments",
        "Allergy and asthma investigations",
        "New home air quality testing",
        "Post-renovation air quality checks",
        "Seasonal air quality monitoring",
        "HVAC system evaluations",
        "Basement and crawl space testing",
        "Indoor plant and pet allergen testing"
      ],
      commercial: [
        "Office building air quality",
        "School and daycare testing",
        "Healthcare facility monitoring",
        "Restaurant and food service testing",
        "Manufacturing facility assessments",
        "Retail space air quality",
        "Multi-tenant building evaluations",
        "Compliance and regulatory testing"
      ]
    },
    commonSigns: {
      title: "When to consider air quality testing:",
      signs: [
        "Unexplained respiratory symptoms",
        "Persistent allergies or asthma",
        "Headaches or fatigue in the space",
        "Unusual odours or stuffiness",
        "Recent construction or renovation",
        "Concerns about mold or chemicals"
      ]
    },
    additionalImages: [
      {
        src: "/images/air-sampling-device.jpg",
        alt: "Air sampling equipment"
      },
      {
        src: "/images/particle-counter.jpg", 
        alt: "Particle counting device"
      },
      {
        src: "/images/hvac-testing.jpg",
        alt: "HVAC system testing"
      },
      {
        src: "/images/air-quality-report.jpg",
        alt: "Detailed air quality report"
      }
    ]
  }

  return <ServicePageTemplate {...pageData} />
}

export default IndoorAirQuality
