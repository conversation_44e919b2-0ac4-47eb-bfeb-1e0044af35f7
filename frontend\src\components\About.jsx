import React, { useState } from 'react'

const tabData = [
  {
    label: 'About Us',
    content: {
      title: 'Pure Cleanup Services',
      text: `At Pure Cleanup Services, we believe that every home and business deserves to be a safe, healthy, and clean environment. When disaster strikes—whether it's mold growth, water damage, or flooding—we're here to restore your property and your peace of mind.`,
      button: true,
    },
  },
  {
    label: 'Our Vision',
    content: {
      title: 'Our Vision',
      text: `To be the most trusted provider of remediation and restoration services, setting the standard for quality, safety, and customer satisfaction in every community we serve.`,
      button: false,
    },
  },
  {
    label: 'Our Mission',
    content: {
      title: 'Our Mission',
      text: `To restore homes and businesses to their best condition through expert remediation, rapid response, and compassionate service—ensuring peace of mind for every client.`,
      button: false,
    },
  },
]

const About = () => {
  const [activeTab, setActiveTab] = useState(0)
  const { title, text, button } = tabData[activeTab].content

  return (
    <section id='about' className='py-16 md:py-20 bg-white'>
      <div className='container-custom'>
        <div className='grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-start'>
          {/* Left Column - About Us Heading and Tabs */}
          <div className='space-y-8'>
            <h2 className='about-heading'>About Us.</h2>
            <div className='flex flex-wrap gap-3'>
              {tabData.map((tab, idx) => (
                <button
                  key={tab.label}
                  onClick={() => setActiveTab(idx)}
                  className={`about-tab-button ${
                    activeTab === idx ? 'active' : 'inactive'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </div>
          </div>

          {/* Right Column - Content */}
          <div className='space-y-6'>
            <h3 className='about-content-title'>{title}</h3>
            <p className='about-content-text'>{text}</p>
            {button && (
              <a href='#contact' className='about-learn-more-button'>
                <span>Learn More</span>
                <span aria-hidden='true'>↗</span>
              </a>
            )}
          </div>
        </div>
      </div>
    </section>
  )
}

export default About
