@tailwind base;
@tailwind components;
@tailwind utilities;

/* Inter Font Family */
@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter/Inter-VariableFont_opsz,wght.ttf')
    format('truetype-variations');
  font-weight: 100 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter/Inter-Italic-VariableFont_opsz,wght.ttf')
    format('truetype-variations');
  font-weight: 100 900;
  font-style: italic;
  font-display: swap;
}

/* Satoshi Font Family */
@font-face {
  font-family: '<PERSON>shi';
  src: url('/fonts/Satoshi_Complete/Satoshi_Complete/Fonts/WEB/fonts/Satoshi-Regular.woff2')
      format('woff2'),
    url('/fonts/Satoshi_Complete/Satoshi_Complete/Fonts/WEB/fonts/Satoshi-Regular.woff')
      format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Satoshi';
  src: url('/fonts/Sato<PERSON>_Complete/Satoshi_Complete/Fonts/WEB/fonts/Satoshi-Medium.woff2')
      format('woff2'),
    url('/fonts/Satoshi_Complete/Satoshi_Complete/Fonts/WEB/fonts/Satoshi-Medium.woff')
      format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Satoshi';
  src: url('/fonts/Satoshi_Complete/Satoshi_Complete/Fonts/WEB/fonts/Satoshi-Bold.woff2')
      format('woff2'),
    url('/fonts/Satoshi_Complete/Satoshi_Complete/Fonts/WEB/fonts/Satoshi-Bold.woff')
      format('woff');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/* Space Grotesk Font Family */
@font-face {
  font-family: 'Space Grotesk';
  src: url('/fonts/Space_Grotesk/SpaceGrotesk-VariableFont_wght.ttf')
    format('truetype-variations');
  font-weight: 300 700;
  font-style: normal;
  font-display: swap;
}

@layer base {
  html {
    scroll-behavior: smooth;
    font-size: 16px; /* Consistent root font size across all environments */
  }

  body {
    font-family: 'Inter', system-ui, sans-serif;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: 'Space Grotesk', system-ui, sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-white border border-primary-600 text-primary-600 hover:bg-primary-50 font-semibold py-3 px-6 rounded-lg transition-colors duration-200;
  }

  .container-custom {
    @apply px-3 sm:px-6 md:px-10 lg:px-14;
  }

  .nav-link {
    font-family: 'Space Grotesk', ui-sans-serif, system-ui !important;
    font-weight: 700 !important;
    font-size: 24px !important;
    line-height: 24px !important;
    letter-spacing: 0% !important;
    text-align: center !important;
  }

  .services-dropdown-item {
    font-family: 'Space Grotesk', ui-sans-serif, system-ui !important;
    font-weight: 700 !important;
    font-size: 20px !important;
    line-height: 150% !important;
    letter-spacing: -2% !important;
  }

  .hero-we-offer {
    font-family: 'Satoshi', ui-sans-serif, system-ui !important;
    font-weight: 300 !important;
    font-size: 30px !important;
    line-height: 120% !important;
    letter-spacing: -2% !important;
  }

  /* Responsive design for hero-we-offer */
  @media (max-width: 768px) {
    .hero-we-offer {
      font-size: 28px !important;
    }
  }

  @media (max-width: 480px) {
    .hero-we-offer {
      font-size: 24px !important;
    }
  }

  .hero-main-heading {
    font-family: 'Satoshi', ui-sans-serif, system-ui !important;
    font-weight: 900 !important;
    font-size: 60px !important;
    line-height: 120% !important;
    letter-spacing: -2% !important;
    text-transform: uppercase !important;
  }

  /* Responsive design for hero-main-heading */
  @media (max-width: 1200px) {
    .hero-main-heading {
      font-size: 40px !important;
    }
  }

  @media (max-width: 768px) {
    .hero-main-heading {
      font-size: 30px !important;
    }
  }

  @media (max-width: 480px) {
    .hero-main-heading {
      font-size: 25px !important;
    }
  }

  .trusted-clients-image {
    width: 244.55px !important;
    height: 62.11px !important;
  }

  /* Responsive design for trusted-clients-image */
  @media (max-width: 768px) {
    .trusted-clients-image {
      width: 200px !important;
      height: 51px !important;
    }
  }

  @media (max-width: 480px) {
    .trusted-clients-image {
      width: 180px !important;
      height: 46px !important;
    }
  }

  .hero-description-text {
    font-family: 'Inter', ui-sans-serif, system-ui !important;
    font-weight: 400 !important;
    font-size: 20px !important;
    line-height: 160% !important;
    letter-spacing: -2% !important;
    text-align: justify !important;
  }

  /* Responsive design for hero-description-text */
  @media (max-width: 1200px) {
    .hero-description-text {
      font-size: 18px !important;
    }
  }

  @media (max-width: 768px) {
    .hero-description-text {
      font-size: 15px !important;
      text-align: left !important;
    }
  }

  @media (max-width: 480px) {
    .hero-description-text {
      font-size: 14px !important;
    }
  }

  .hero-cta-button {
    width: 280px !important;
    height: 65px !important;
    gap: 5.18px !important;
    padding: 18px 36px !important;
    border-radius: 10.35px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  /* Responsive design for hero-cta-button */
  @media (max-width: 1200px) {
    .hero-cta-button {
      width: 260px !important;
      height: 60px !important;
      padding: 16px 32px !important;
    }
  }

  @media (max-width: 768px) {
    .hero-cta-button {
      width: 240px !important;
      height: 55px !important;
      padding: 14px 28px !important;
      border-radius: 8px !important;
    }
  }

  @media (max-width: 480px) {
    .hero-cta-button {
      width: 200px !important;
      height: 50px !important;
      padding: 12px 24px !important;
      gap: 4px !important;
    }
  }

  /* About Section Styles */
  .about-heading {
    font-family: 'Satoshi', ui-sans-serif, system-ui !important;
    font-weight: 700 !important;
    font-size: 48px !important;
    line-height: 120% !important;
    letter-spacing: -2% !important;
    color: #1f2937 !important;
  }

  .about-tab-button {
    font-family: 'Satoshi', ui-sans-serif, system-ui !important;
    font-weight: 500 !important;
    font-size: 16px !important;
    line-height: 140% !important;
    padding: 12px 24px !important;
    border-radius: 50px !important;
    border: 2px solid !important;
    transition: all 0.3s ease !important;
  }

  .about-tab-button.active {
    background-color: #1f2937 !important;
    color: white !important;
    border-color: #1f2937 !important;
  }

  .about-tab-button.inactive {
    background-color: transparent !important;
    color: #9ca3af !important;
    border-color: #d1d5db !important;
  }

  .about-tab-button.inactive:hover {
    color: #1f2937 !important;
    border-color: #6b7280 !important;
  }

  .about-content-title {
    font-family: 'Satoshi', ui-sans-serif, system-ui !important;
    font-weight: 700 !important;
    font-size: 32px !important;
    line-height: 120% !important;
    letter-spacing: -1% !important;
    color: #1f2937 !important;
  }

  .about-content-text {
    font-family: 'Satoshi', ui-sans-serif, system-ui !important;
    font-weight: 400 !important;
    font-size: 20px !important;
    line-height: 160% !important;
    color: #4b5563 !important;
    text-align: justify !important;
  }

  .about-learn-more-button {
    font-family: 'Satoshi', ui-sans-serif, system-ui !important;
    font-weight: 600 !important;
    font-size: 16px !important;
    padding: 14px 28px !important;
    background-color: #16a34a !important;
    color: white !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
  }

  .about-learn-more-button:hover {
    background-color: #15803d !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 16px rgba(22, 163, 74, 0.3) !important;
  }

  /* Responsive design for About section */
  @media (max-width: 1024px) {
    .about-heading {
      font-size: 40px !important;
    }

    .about-content-title {
      font-size: 28px !important;
    }

    .about-content-text {
      font-size: 16px !important;
    }
  }

  @media (max-width: 768px) {
    .about-heading {
      font-size: 32px !important;
    }

    .about-tab-button {
      font-size: 14px !important;
      padding: 10px 20px !important;
    }

    .about-content-title {
      font-size: 24px !important;
    }

    .about-content-text {
      font-size: 15px !important;
    }
  }

  @media (max-width: 480px) {
    .about-heading {
      font-size: 28px !important;
    }

    .about-tab-button {
      font-size: 12px !important;
      padding: 8px 16px !important;
    }

    .about-content-title {
      font-size: 20px !important;
    }

    .about-content-text {
      font-size: 14px !important;
    }

    .about-learn-more-button {
      font-size: 14px !important;
      padding: 12px 24px !important;
    }
  }

  /* Services Section Styles */
  .services-card {
    background: white;
    border-radius: 16px;
    border: 2px solid #16a34a;
    padding: 40px 32px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 400px;
  }

  .services-card:hover {
    box-shadow: 0 20px 40px rgba(22, 163, 74, 0.15);
    transform: translateY(-4px);
  }

  .services-icon {
    width: 80px !important;
    height: 80px !important;
    object-fit: contain;
    margin-bottom: 24px !important;
    padding: 16px;
    background: rgba(22, 163, 74, 0.1);
    border-radius: 12px;
  }

  .services-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .services-title {
    font-family: 'Satoshi', ui-sans-serif, system-ui !important;
    font-weight: 700 !important;
    font-size: 28px !important;
    line-height: 130% !important;
    color: #1f2937 !important;
    margin-bottom: 20px !important;
  }

  .services-description {
    font-family: 'Satoshi', ui-sans-serif, system-ui !important;
    font-weight: 400 !important;
    font-size: 24px !important;
    line-height: 36px !important;
    letter-spacing: 0% !important;
    color: #4b5563 !important;
    margin-bottom: 24px !important;
    text-align: left;
  }

  .services-features {
    width: 100%;
    text-align: left;
  }

  .services-feature-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 12px;
    font-family: 'Satoshi', ui-sans-serif, system-ui !important;
    font-weight: 400 !important;
    font-size: 15px !important;
    line-height: 140% !important;
    color: #374151 !important;
  }

  .services-feature-icon {
    width: 20px !important;
    height: 20px !important;
    color: #16a34a !important;
    margin-right: 12px !important;
    margin-top: 2px !important;
    flex-shrink: 0;
  }

  /* Responsive design for Services section */
  @media (max-width: 1024px) {
    .services-card {
      padding: 32px 24px;
      min-height: 380px;
    }

    .services-icon {
      width: 70px !important;
      height: 70px !important;
      margin-bottom: 20px !important;
      padding: 14px;
    }

    .services-title {
      font-size: 24px !important;
      margin-bottom: 16px !important;
    }

    .services-description {
      font-size: 20px !important;
      line-height: 30px !important;
      letter-spacing: 0% !important;
      margin-bottom: 20px !important;
    }

    .services-feature-item {
      font-size: 14px !important;
      margin-bottom: 10px;
    }
  }

  @media (max-width: 768px) {
    .services-card {
      padding: 28px 20px;
      min-height: 350px;
    }

    .services-icon {
      width: 60px !important;
      height: 60px !important;
      margin-bottom: 16px !important;
      padding: 12px;
    }

    .services-title {
      font-size: 22px !important;
      margin-bottom: 14px !important;
    }

    .services-description {
      font-size: 18px !important;
      line-height: 27px !important;
      letter-spacing: 0% !important;
      margin-bottom: 18px !important;
    }

    .services-feature-item {
      font-size: 13px !important;
      margin-bottom: 8px;
    }

    .services-feature-icon {
      width: 16px !important;
      height: 16px !important;
      margin-right: 10px !important;
    }
  }

  @media (max-width: 480px) {
    .services-card {
      padding: 24px 16px;
      min-height: 320px;
    }

    .services-icon {
      width: 50px !important;
      height: 50px !important;
      margin-bottom: 14px !important;
      padding: 10px;
    }

    .services-title {
      font-size: 20px !important;
      margin-bottom: 12px !important;
    }

    .services-description {
      font-size: 13px !important;
      margin-bottom: 16px !important;
    }

    .services-feature-item {
      font-size: 12px !important;
      margin-bottom: 6px;
    }
  }

  /* Why Choose Us Section Styles */
  .why-choose-us-heading {
    font-family: 'Satoshi', ui-sans-serif, system-ui !important;
    font-weight: 900 !important;
    font-size: 80px !important;
    line-height: 120% !important;
    letter-spacing: -2% !important;
    color: #1f2937 !important;
  }

  .why-choose-us-description {
    font-family: 'Space Grotesk', ui-sans-serif, system-ui !important;
    font-weight: 400 !important;
    font-size: 22px !important;
    line-height: 36px !important;
    letter-spacing: 0% !important;
    color: #4b5563 !important;
    text-align: left !important;
  }

  .why-choose-us-card {
    background: white !important;
    border-radius: 16px !important;
    border: 2px solid #16a34a !important;
    padding: 24px 20px !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05) !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    flex-direction: column !important;
    width: 300px !important;
    height: 280px !important;
    text-align: left !important;
  }

  .why-choose-us-card:hover {
    box-shadow: 0 20px 40px rgba(22, 163, 74, 0.15) !important;
    transform: translateY(-4px) !important;
  }

  .why-choose-us-icon-container {
    width: 60px !important;
    height: 60px !important;
    background: rgba(22, 163, 74, 0.1) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin-bottom: 20px !important;
  }

  .why-choose-us-icon {
    width: 28px !important;
    height: 28px !important;
    color: #16a34a !important;
  }

  .why-choose-us-content {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
  }

  .why-choose-us-card-title {
    font-family: 'Satoshi', ui-sans-serif, system-ui !important;
    font-weight: 600 !important;
    font-size: 20px !important;
    line-height: 140% !important;
    color: #1f2937 !important;
    margin-bottom: 12px !important;
  }

  .why-choose-us-card-description {
    font-family: 'Satoshi', ui-sans-serif, system-ui !important;
    font-weight: 400 !important;
    font-size: 16px !important;
    line-height: 160% !important;
    color: #6b7280 !important;
    flex: 1 !important;
  }

  /* Responsive design for Why Choose Us section */
  @media (max-width: 1024px) {
    .why-choose-us-heading {
      font-size: 60px !important;
    }

    .why-choose-us-description {
      font-size: 20px !important;
      line-height: 32px !important;
    }

    .why-choose-us-card {
      padding: 20px 16px !important;
      width: 280px !important;
      height: 250px !important;
    }

    .why-choose-us-icon-container {
      width: 50px !important;
      height: 50px !important;
      margin-bottom: 16px !important;
    }

    .why-choose-us-icon {
      width: 24px !important;
      height: 24px !important;
    }

    .why-choose-us-card-title {
      font-size: 18px !important;
      margin-bottom: 10px !important;
    }

    .why-choose-us-card-description {
      font-size: 15px !important;
    }
  }

  @media (max-width: 768px) {
    .why-choose-us-heading {
      font-size: 48px !important;
    }

    .why-choose-us-description {
      font-size: 18px !important;
      line-height: 29px !important;
    }

    .why-choose-us-card {
      padding: 18px 14px !important;
      width: 250px !important;
      height: 220px !important;
    }

    .why-choose-us-icon-container {
      width: 45px !important;
      height: 45px !important;
      margin-bottom: 14px !important;
    }

    .why-choose-us-icon {
      width: 20px !important;
      height: 20px !important;
    }

    .why-choose-us-card-title {
      font-size: 16px !important;
      margin-bottom: 8px !important;
    }

    .why-choose-us-card-description {
      font-size: 14px !important;
    }
  }

  @media (max-width: 480px) {
    .why-choose-us-heading {
      font-size: 40px !important;
    }

    .why-choose-us-description {
      font-size: 16px !important;
      line-height: 26px !important;
    }

    .why-choose-us-card {
      padding: 16px 12px !important;
      width: 100% !important;
      height: 200px !important;
      max-width: 300px !important;
    }

    .why-choose-us-icon-container {
      width: 40px !important;
      height: 40px !important;
      margin-bottom: 12px !important;
    }

    .why-choose-us-icon {
      width: 18px !important;
      height: 18px !important;
    }

    .why-choose-us-card-title {
      font-size: 15px !important;
      margin-bottom: 6px !important;
    }

    .why-choose-us-card-description {
      font-size: 13px !important;
    }
  }

  /* Testimonials Responsive Cards - Large Dimensions */
  @media (max-width: 1600px) {
    .testimonial-card {
      transform: scale(0.8) !important;
    }
  }

  @media (max-width: 1400px) {
    .testimonial-card {
      transform: scale(0.7) !important;
    }
  }

  @media (max-width: 1200px) {
    .testimonial-card {
      transform: scale(0.6) !important;
    }
  }

  @media (max-width: 1024px) {
    .testimonial-card {
      transform: scale(0.5) !important;
    }
  }

  @media (max-width: 768px) {
    .testimonial-card {
      transform: scale(0.4) !important;
    }
  }

  @media (max-width: 640px) {
    .testimonial-card {
      transform: scale(0.35) !important;
    }
  }

  @media (max-width: 480px) {
    .testimonial-card {
      transform: scale(0.3) !important;
    }
  }
}
