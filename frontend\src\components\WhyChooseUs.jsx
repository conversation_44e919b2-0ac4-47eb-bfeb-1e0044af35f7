import React from 'react'
import {
  ShieldCheckIcon,
  ClockIcon,
  SparklesIcon,
  UsersIcon,
} from '@heroicons/react/24/outline'

const WhyChooseUs = () => {
  const features = [
    {
      icon: ShieldCheckIcon,
      title: 'Certified Professionals',
      description: 'Our team is fully certified, ensuring the highest standards of quality and safety in every project',
    },
    {
      icon: ClockIcon,
      title: 'Fast Response',
      description: "We're available around the clock to respond to emergencies, and restoring your property quickly.",
    },
    {
      icon: SparklesIcon,
      title: 'Eco-Friendly Methods',
      description: 'We use advanced, environmentally safe techniques to clean and restore your property.',
    },
    {
      icon: UsersIcon,
      title: 'Customer-Focused Service',
      description: 'Your satisfaction is our priority we treat your property as if it were our own, delivering personalized care.',
    },
  ]

  return (
    <section className="py-16 md:py-20 bg-[#FAFDF6]">
      <div className="container-custom mx-auto">
        {/* Header */}
        <div className="max-w-4xl mb-12 md:mb-16">
          <h2 className="why-choose-us-heading mb-6">Why Choose Us</h2>
          <p className="why-choose-us-description">
            Choose us for certified expertise, 24/7 emergency response, eco-friendly solutions, and a commitment to putting your needs first.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 justify-center md:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8">
          {features.map((feature) => (
            <div
              key={feature.title}
              className="why-choose-us-card"
            >
              {/* Icon */}
              <div className="why-choose-us-icon-container">
                <feature.icon className="why-choose-us-icon" />
              </div>

              {/* Content */}
              <div className="why-choose-us-content">
                <h3 className="why-choose-us-card-title">{feature.title}</h3>
                <p className="why-choose-us-card-description">{feature.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default WhyChooseUs
