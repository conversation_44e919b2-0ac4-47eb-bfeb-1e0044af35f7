import React from 'react'
import { CheckIcon } from '@heroicons/react/24/outline'
import ImageSlider from './ImageSlider'

const ServicePageTemplate = ({
  heroTitle,
  heroSubtitle,
  heroImages,
  serviceTitle,
  serviceDescription,
  serviceFeatures,
  focusAreas,
  commonSigns,
  additionalImages,
}) => {
  return (
    <div className='bg-white'>
      {/* Hero Section with Image Slider */}
      <ImageSlider
        images={heroImages}
        title={heroTitle}
        subtitle={heroSubtitle}
      />

      {/* Service Overview Section */}
      <section className='py-16 md:py-20 bg-white'>
        <div className='container-custom'>
          <div className='grid grid-cols-1 lg:grid-cols-2 gap-12 items-start max-w-6xl mx-auto'>
            {/* Left Column - Text Content */}
            <div>
              <div className='mb-6'>
                <span className='text-orange-500 text-sm font-semibold uppercase tracking-wide'>
                  What We Offer
                </span>
              </div>
              <h1 className='text-3xl md:text-4xl font-bold text-gray-900 mb-6'>
                {serviceTitle}
              </h1>
              <p className='text-gray-600 leading-relaxed mb-8'>
                {serviceDescription}
              </p>

              {/* Service Features */}
              {serviceFeatures && serviceFeatures.length > 0 && (
                <div className='space-y-3 mb-8'>
                  {serviceFeatures.slice(0, 6).map((feature, index) => (
                    <div key={index} className='flex items-start space-x-3'>
                      <CheckIcon className='w-5 h-5 text-green-600 flex-shrink-0 mt-0.5' />
                      <p className='text-gray-700 text-sm'>{feature}</p>
                    </div>
                  ))}
                </div>
              )}

              <button className='bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200'>
                Learn More →
              </button>
            </div>

            {/* Right Column - Images */}
            <div className='grid grid-cols-2 gap-4'>
              {additionalImages?.slice(0, 4).map((image, index) => (
                <div
                  key={index}
                  className='rounded-lg overflow-hidden shadow-lg'
                >
                  <img
                    src={image.src}
                    alt={image.alt}
                    className='w-full h-32 md:h-40 object-cover hover:scale-105 transition-transform duration-300'
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Focus Areas Section */}
      <section className='py-16 md:py-20 bg-gray-900 text-white relative'>
        <div className='container-custom'>
          <div className='mb-12'>
            <h2 className='text-3xl md:text-4xl font-bold mb-4'>Focus Area</h2>
            <p className='text-gray-300 max-w-2xl'>
              We provide comprehensive services for both residential and
              commercial properties, ensuring quality results for every project.
            </p>
          </div>

          <div className='grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto'>
            {/* Residential */}
            <div>
              <h3 className='text-2xl font-bold mb-6'>
                Residential <span className='text-green-500'>Services</span>
              </h3>
              <ul className='space-y-4'>
                {focusAreas?.residential?.map((item, index) => (
                  <li key={index} className='flex items-start space-x-3'>
                    <div className='w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0'></div>
                    <span className='text-gray-300'>{item}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Commercial */}
            <div>
              <h3 className='text-2xl font-bold mb-6'>
                Commercial <span className='text-green-500'>Services</span>
              </h3>
              <ul className='space-y-4'>
                {focusAreas?.commercial?.map((item, index) => (
                  <li key={index} className='flex items-start space-x-3'>
                    <div className='w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0'></div>
                    <span className='text-gray-300'>{item}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Common Signs Section */}
      {commonSigns && (
        <section className='py-16 md:py-20 bg-white'>
          <div className='container-custom'>
            <div className='grid grid-cols-1 lg:grid-cols-2 gap-12 items-center max-w-6xl mx-auto'>
              {/* Text Content */}
              <div>
                <h2 className='text-3xl md:text-4xl font-bold text-gray-900 mb-6'>
                  {commonSigns.title}
                </h2>
                <ul className='space-y-4'>
                  {commonSigns.signs.map((sign, index) => (
                    <li key={index} className='flex items-start space-x-3'>
                      <CheckIcon className='w-6 h-6 text-green-600 flex-shrink-0 mt-1' />
                      <span className='text-gray-700 text-lg'>{sign}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Images Grid */}
              <div className='grid grid-cols-2 gap-4'>
                {additionalImages?.map((image, index) => (
                  <div
                    key={index}
                    className='rounded-lg overflow-hidden shadow-lg'
                  >
                    <img
                      src={image.src}
                      alt={image.alt}
                      className='w-full h-48 object-cover hover:scale-105 transition-transform duration-300'
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>
      )}
    </div>
  )
}

export default ServicePageTemplate
