import React from 'react'
import { CheckIcon } from '@heroicons/react/24/outline'
import ImageSlider from './ImageSlider'
import CTA from './CTA'
import Contact from './Contact'

const ServicePageTemplate = ({ 
  heroTitle, 
  heroSubtitle, 
  heroImages,
  serviceTitle,
  serviceDescription,
  serviceFeatures,
  focusAreas,
  commonSigns,
  additionalImages 
}) => {
  return (
    <div className="bg-white">
      {/* Hero Section with Image Slider */}
      <ImageSlider 
        images={heroImages}
        title={heroTitle}
        subtitle={heroSubtitle}
      />

      {/* Service Overview Section */}
      <section className="py-16 md:py-20 bg-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                {serviceTitle}
              </h1>
              <p className="text-lg md:text-xl text-gray-600 leading-relaxed">
                {serviceDescription}
              </p>
            </div>

            {/* Service Features */}
            {serviceFeatures && serviceFeatures.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-16">
                {serviceFeatures.map((feature, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <CheckIcon className="w-6 h-6 text-green-600 flex-shrink-0 mt-1" />
                    <p className="text-gray-700">{feature}</p>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Focus Areas Section */}
      <section className="py-16 md:py-20 bg-gray-50">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Focus Area
            </h2>
            <p className="text-lg text-gray-600">
              We provide comprehensive services for both residential and commercial properties
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {/* Residential */}
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">
                Residential <span className="text-green-600">Services</span>
              </h3>
              <ul className="space-y-3">
                {focusAreas?.residential?.map((item, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <CheckIcon className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">{item}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Commercial */}
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">
                Commercial <span className="text-green-600">Services</span>
              </h3>
              <ul className="space-y-3">
                {focusAreas?.commercial?.map((item, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <CheckIcon className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">{item}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Common Signs Section */}
      {commonSigns && (
        <section className="py-16 md:py-20 bg-white">
          <div className="container-custom">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center max-w-6xl mx-auto">
              {/* Text Content */}
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                  {commonSigns.title}
                </h2>
                <ul className="space-y-4">
                  {commonSigns.signs.map((sign, index) => (
                    <li key={index} className="flex items-start space-x-3">
                      <CheckIcon className="w-6 h-6 text-green-600 flex-shrink-0 mt-1" />
                      <span className="text-gray-700 text-lg">{sign}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Images Grid */}
              <div className="grid grid-cols-2 gap-4">
                {additionalImages?.map((image, index) => (
                  <div key={index} className="rounded-lg overflow-hidden shadow-lg">
                    <img
                      src={image.src}
                      alt={image.alt}
                      className="w-full h-48 object-cover hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>
      )}

      {/* CTA and Contact Sections */}
      <CTA />
      <Contact />
    </div>
  )
}

export default ServicePageTemplate
