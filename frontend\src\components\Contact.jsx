import React, { useState } from 'react'
import { ArrowRightIcon } from '@heroicons/react/24/solid'

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)
    setTimeout(() => {
      alert('Thank you! Your message has been sent.')
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: '',
      })
      setIsSubmitting(false)
    }, 1500)
  }

  return (
    <section id='contact' className='py-8 md:py-10 lg:py-12 bg-white'>
      <div className='container-custom px-4 md:px-6 lg:px-10'>
        {/* Header */}
        <div className='mx-auto mb-8 md:mb-10'>
          <h2 className='text-2xl md:text-4xl lg:text-5xl xl:text-5xl font-bold text-black mb-3 leading-tight'>
            Have an idea? Let's talk.
          </h2>
          <p className='text-sm md:text-base lg:text-xl text-black'>
            Cum et convellis risus placerat aliquam, nunc. Scelerisque aliquet
            faucibus tincidunt eu adipiscing sociis arcu lorem porttitor.
          </p>
        </div>

        {/* Form */}
        <div className='mx-auto'>
          <form onSubmit={handleSubmit} className='space-y-4 md:space-y-5'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-5'>
              {/* Full Name */}
              <div>
                <label
                  htmlFor='name'
                  className='block text-sm md:text-base lg:text-[20px] font-satoshi font-medium text-black mb-1'
                >
                  Full Name
                </label>
                <input
                  type='text'
                  name='name'
                  id='name'
                  placeholder='e.g. Alex Tolu'
                  required
                  className='w-full px-4 md:px-6 lg:px-8 py-3 md:py-4 border text-black border-gray-200 rounded-md text-sm md:text-base lg:text-[14px] placeholder-gray-400 focus:border-gray-300 focus:outline-none focus:ring-0 transition-colors'
                  value={formData.name}
                  onChange={handleInputChange}
                />
              </div>

              {/* Email Address */}
              <div>
                <label
                  htmlFor='email'
                  className='block text-sm md:text-base lg:text-[20px] font-satoshi font-medium text-black mb-1'
                >
                  Email Address
                </label>
                <input
                  type='email'
                  name='email'
                  id='email'
                  placeholder='e.g. <EMAIL>'
                  required
                  className='w-full px-4 md:px-6 lg:px-8 py-3 md:py-4 border text-black border-gray-200 rounded-md text-sm md:text-base lg:text-[14px] placeholder-gray-400 focus:border-gray-300 focus:outline-none focus:ring-0 transition-colors'
                  value={formData.email}
                  onChange={handleInputChange}
                />
              </div>

              {/* Phone Number */}
              <div>
                <label
                  htmlFor='phone'
                  className='block text-sm md:text-base lg:text-[20px] font-satoshi font-medium text-black mb-1'
                >
                  Phone Number
                </label>
                <input
                  type='tel'
                  name='phone'
                  id='phone'
                  placeholder='+000'
                  className='w-full px-4 md:px-6 lg:px-8 py-3 md:py-4 border text-black border-gray-200 rounded-md text-sm md:text-base lg:text-[14px] placeholder-gray-400 focus:border-gray-300 focus:outline-none focus:ring-0 transition-colors'
                  value={formData.phone}
                  onChange={handleInputChange}
                />
              </div>

              {/* Subject */}
              <div>
                <label
                  htmlFor='subject'
                  className='block text-sm md:text-base lg:text-[20px] font-satoshi font-medium text-black mb-1'
                >
                  Subject
                </label>
                <input
                  type='text'
                  name='subject'
                  id='subject'
                  placeholder='Input'
                  required
                  className='w-full px-4 md:px-6 lg:px-8 py-3 md:py-4 border text-black border-gray-200 rounded-md text-sm md:text-base lg:text-[14px] placeholder-gray-400 focus:border-gray-300 focus:outline-none focus:ring-0 transition-colors'
                  value={formData.subject}
                  onChange={handleInputChange}
                />
              </div>
            </div>

            {/* Message */}
            <div>
              <label
                htmlFor='message'
                className='block text-sm md:text-base lg:text-[20px] font-satoshi font-medium text-black mb-1'
              >
                Message
              </label>
              <textarea
                name='message'
                id='message'
                rows='6'
                placeholder='Enter your message...'
                required
                className='w-full px-4 md:px-6 lg:px-8 py-3 md:py-4 text-black border border-gray-200 rounded-md text-sm md:text-base lg:text-[14px] placeholder-gray-400 focus:border-gray-300 focus:outline-none focus:ring-0 transition-colors resize-none'
                value={formData.message}
                onChange={handleInputChange}
              />
            </div>

            {/* Submit */}
            <div className='flex justify-center md:justify-end pt-2'>
              <button
                type='submit'
                disabled={isSubmitting}
                className='w-full md:w-auto inline-flex items-center justify-center bg-[#009D22] hover:bg-green-600 text-white font-bold py-3 md:py-4 px-6 md:px-8 rounded-md transition-all duration-200 text-sm md:text-base lg:text-[15px] shadow-lg hover:shadow-xl transform hover:-translate-y-1 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none'
              >
                {isSubmitting ? 'Submitting...' : 'Submit'} ↗
                {/* {!isSubmitting && <ArrowRightIcon className='w-4 h-4 ml-3' />} */}
              </button>
            </div>
          </form>
        </div>
      </div>
    </section>
  )
}

export default Contact
