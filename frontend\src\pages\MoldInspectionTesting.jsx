import React from 'react'
import ServicePageTemplate from '../components/ServicePageTemplate'

const MoldInspectionTesting = () => {
  const pageData = {
    heroTitle: "Mold Inspection and Testing",
    heroSubtitle: "Comprehensive mold detection and air quality testing",
    heroImages: [
      {
        src: "/images/inspection-hero-1.jpg",
        alt: "Professional mold inspection in progress"
      },
      {
        src: "/images/inspection-hero-2.jpg", 
        alt: "Advanced mold testing equipment"
      },
      {
        src: "/images/inspection-hero-3.jpg",
        alt: "Laboratory analysis of mold samples"
      }
    ],
    serviceTitle: "Mold Inspection and Testing",
    serviceDescription: "Early detection is key to preventing extensive mold damage and health issues. Our certified mold inspectors use advanced testing methods and equipment to identify mold presence, determine the type and concentration, and provide detailed reports with recommendations for remediation.",
    serviceFeatures: [
      "Comprehensive visual mold inspection",
      "Air quality sampling and analysis",
      "Surface sampling and testing", 
      "Moisture mapping and detection",
      "Thermal imaging for hidden moisture",
      "Laboratory analysis and reporting",
      "Post-remediation verification testing",
      "Indoor air quality assessment"
    ],
    focusAreas: {
      residential: [
        "Pre-purchase home inspections",
        "Post-water damage assessments",
        "Routine air quality testing",
        "Hidden mold detection",
        "HVAC system inspection",
        "Basement and crawl space testing",
        "Attic and roof leak inspection",
        "Health-related mold concerns"
      ],
      commercial: [
        "Building acquisition inspections",
        "Tenant complaint investigations",
        "Insurance claim documentation",
        "Compliance testing and reporting",
        "Large facility assessments",
        "Multi-unit property testing",
        "Industrial air quality monitoring",
        "Healthcare facility testing"
      ]
    },
    commonSigns: {
      title: "When you should consider mold testing:",
      signs: [
        "Unexplained health symptoms",
        "Musty odors without visible mold",
        "Recent water damage or leaks",
        "High humidity or moisture issues",
        "Before purchasing a property",
        "After mold remediation completion"
      ]
    },
    additionalImages: [
      {
        src: "/images/air-sampling.jpg",
        alt: "Air quality sampling equipment"
      },
      {
        src: "/images/moisture-meter.jpg", 
        alt: "Professional moisture detection"
      },
      {
        src: "/images/lab-testing.jpg",
        alt: "Laboratory mold analysis"
      },
      {
        src: "/images/inspection-report.jpg",
        alt: "Detailed inspection report"
      }
    ]
  }

  return <ServicePageTemplate {...pageData} />
}

export default MoldInspectionTesting
