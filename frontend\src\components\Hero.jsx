import React from 'react'
import { ArrowUpRightIcon } from '@heroicons/react/24/solid'

const Hero = () => {
  return (
    <section
      id='home'
      className='relative bg-[#212121] text-white min-h-[400px] h-auto md:min-h-[500px] flex items-center pt-7'
      style={{ backgroundImage: "url('/images/Group-3.png')" }}
    >
      {/* <div className='absolute inset-0 bg-black opacity-60'></div> */}
      <div className='container-custom relative z-10 py-16 md:py-20'>
        <div className='grid grid-cols-1 md:grid-cols-5 gap-8 md:gap-12 items-center'>
          {/* Left Column */}
          <div className='md:col-span-3 mb-8 md:mb-0'>
            <p className='hero-we-offer text-[#FF9542]'>We Offer</p>
            <h1 className='hero-main-heading text-white leading-tight mt-2 md:mt-3 pl-2'>
              RELIABLE MOLD REMEDIATION AND FLOOD DAMAGE RESTORATION SERVICES
            </h1>
          </div>

          {/* Right Column */}
          <div className='md:col-span-2 flex flex-col items-start space-y-4 self-center md:self-end w-full max-w-xs md:max-w-sm'>
            <div className='flex items-center'>
              <img
                src='/images/TrustedClients.png'
                alt='Trusted Clients'
                className='trusted-clients-image'
              />
            </div>
            <p className='hero-description-text text-gray-300'>
              Join thousands of people who trust our services to Restore your
              home or business to its original condition, quickly and safely
            </p>
            <a
              href='#contact'
              className='hero-cta-button btn-primary bg-[#009D22] hover:bg-green-600 text-sm font-semibold'
            >
              <span>Contact us Today</span>
              <ArrowUpRightIcon className='w-4 h-4 ml-1' />
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Hero
