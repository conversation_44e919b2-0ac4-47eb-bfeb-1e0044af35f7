import React, { useState } from 'react'
import { ArrowLeftIcon, ArrowRightIcon } from '@heroicons/react/24/outline'
import { StarIcon } from '@heroicons/react/24/solid'

const Testimonials = () => {
  const [currentIndex, setCurrentIndex] = useState(0)

  const testimonials = [
    {
      name: '<PERSON>',
      text: "I can't describe how relieved we feel using Pure Cleanup Services. They completely transformed our property after the damage, and the time we saved by trusting their expertise was incredible. Top-notch service",
      image:
        'https://images.unsplash.com/photo-1580489944761-15a19d654956?q=80&w=1961',
    },
    {
      name: '<PERSON>',
      text: 'Exceptional service from start to finish. The team was professional, efficient, and restored our home to perfect condition. Highly recommended.',
      image:
        'https://images.unsplash.com/photo-1557862921-37829c790f19?q=80&w=2071',
    },
    {
      name: '<PERSON>',
      text: 'Pure Cleanup handled our flood damage with incredible speed and care. Their attention to detail and customer support were outstanding.',
      image:
        'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?q=80&w=2070',
    },
  ]

  const handlePrev = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1
    )
  }

  const handleNext = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
    )
  }

  // Helper to get the correct testimonial for each card
  const getTestimonial = (offset) => {
    const idx =
      (currentIndex + offset + testimonials.length) % testimonials.length
    return testimonials[idx]
  }

  // Card width and height - responsive sizing
  const cardWidth = window.innerWidth < 768 ? 280 : 1000
  const cardHeight = window.innerWidth < 768 ? 320 : 420
  const visiblePercent = 0.1
  const visibleWidth = cardWidth * visiblePercent
  const hiddenWidth = cardWidth - visibleWidth

  return (
    <section id='testimonials' className='py-16 bg-white'>
      <div className=''>
        <div className='container-custom'>
          {/* Header Row */}
          <div className='flex flex-col md:flex-row md:items-center md:justify-between mb-2'>
            <div>
              <h2 className='text-5xl md:text-6xl font-bold text-gray-900 leading-tight mb-4'>
                The <span className='text-orange-400'>Customer is Hero</span>
                <br />
                of Our Business
              </h2>
              <p className='text-lg md:text-xl text-gray-700 mt-4'>
                Cum et convallis risus placerat aliquam, nunc. Scelerisque
                aliquet faucibus tincidunt eu
              </p>
            </div>
            <div className='flex space-x-4 mt-6 md:mt-0'>
              <button
                onClick={handlePrev}
                className='w-12 h-12 border border-gray-400 rounded-full flex items-center justify-center text-gray-700 hover:bg-gray-100 transition-all duration-300 hover:scale-110 hover:border-gray-600'
                aria-label='Previous testimonial'
              >
                <ArrowLeftIcon className='w-6 h-6 transition-all duration-200' />
              </button>
              <button
                onClick={handleNext}
                className='w-12 h-12 border border-orange-400 rounded-full flex items-center justify-center text-orange-400 hover:bg-orange-50 transition-all duration-300 hover:scale-110 hover:border-orange-600'
                aria-label='Next testimonial'
              >
                <ArrowRightIcon className='w-6 h-6 transition-all duration-200' />
              </button>
            </div>
          </div>
        </div>

        {/* Testimonial Carousel - 3 cards in a row, only 10% of side cards visible */}
        <div
          className='relative flex justify-center items-center overflow-hidden w-full px-4 md:px-0'
          style={{ height: window.innerWidth < 768 ? '360px' : '480px' }}
        >
          {/* Left Card (90% offscreen left) */}
          <div
            className='absolute top-1/2 left-0 z-0 opacity-60 scale-90 bg-[#BBBCBC] rounded-xl md:rounded-3xl shadow-lg flex flex-col justify-center overflow-hidden transition-all duration-700 ease-in-out transform'
            style={{
              width: `${cardWidth}px`,
              height: `${cardHeight}px`,
              transform: `translate(-${hiddenWidth}px, -50%)`,
              padding: window.innerWidth < 768 ? '16px 20px' : '32px 48px',
            }}
          >
            {/* Orange swirl SVG background */}
            <svg
              className='absolute inset-0 w-full h-full pointer-events-none opacity-30'
              viewBox='0 0 900 400'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
            >
              <path
                d='M100 400C200 120 700 120 800 400'
                stroke='#FFB347'
                strokeWidth='2'
                fill='none'
              />
              <path
                d='M200 400C300 180 600 180 700 400'
                stroke='#FFB347'
                strokeWidth='2'
                fill='none'
              />
              <path
                d='M300 400C350 240 550 240 600 400'
                stroke='#FFB347'
                strokeWidth='2'
                fill='none'
              />
            </svg>
            <img
              src='/images/inverted_comma.png'
              alt='inverted comma'
              className='absolute w-12 h-12 md:w-20 md:h-20 opacity-40 pointer-events-none left-4 top-4 md:left-10 md:top-10 z-10'
            />
            <blockquote className='text-sm md:text-xl lg:text-2xl text-white text-center font-light leading-relaxed truncate relative z-10 transition-all duration-500'>
              {getTestimonial(-1).text}
            </blockquote>
            {/* Bottom Row: Avatar, Name, Stars */}
            <div className='flex items-center space-x-2 md:space-x-4 mt-4 md:mt-6 z-10 justify-center transition-all duration-500'>
              <img
                src={getTestimonial(-1).image}
                alt={getTestimonial(-1).name}
                className='w-8 h-8 md:w-14 md:h-14 rounded-full object-cover border border-white opacity-80'
              />
              <div>
                <div className='text-sm md:text-lg font-semibold text-[#F5E6E2] opacity-80'>
                  {getTestimonial(-1).name}
                </div>
                <div className='flex items-center mt-1 md:mt-2'>
                  {[...Array(5)].map((_, i) => (
                    <StarIcon
                      key={i}
                      className='w-3 h-3 md:w-4 md:h-4 text-yellow-400 opacity-80'
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Center Card (active) - with msg.png background, NO orange ripple */}
          <div
            className='relative z-10 rounded-xl md:rounded-3xl shadow-lg flex flex-col justify-between transition-all duration-700 ease-in-out transform hover:scale-105 mx-4 md:mx-0'
            style={{
              width: `${cardWidth}px`,
              height: `${cardHeight}px`,
              padding: window.innerWidth < 768 ? '20px 24px' : '48px 48px',
              backgroundImage: 'url(/images/msg.png)',
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat',
            }}
          >
            <img
              src='/images/inverted_comma.png'
              alt='inverted comma'
              className='absolute w-12 h-12 md:w-20 md:h-20 opacity-40 pointer-events-none left-4 top-4 md:left-10 md:top-10 z-10'
            />
            <blockquote className='text-sm md:text-2xl lg:text-3xl text-white text-center font-light leading-relaxed z-10 transition-all duration-500 opacity-100'>
              {getTestimonial(0).text}
            </blockquote>
            {/* Bottom Row: Avatar, Name, Stars, Quote Icon */}
            <div className='flex items-end justify-between mt-4 md:mt-8 z-10 transition-all duration-500'>
              <div className='flex items-center space-x-2 md:space-x-4'>
                <img
                  src={getTestimonial(0).image}
                  alt={getTestimonial(0).name}
                  className='w-10 h-10 md:w-16 md:h-16 rounded-full object-cover border-2 border-white transition-all duration-300 hover:border-orange-400'
                />
                <div>
                  <div className='text-sm md:text-lg font-semibold text-[#F5E6E2] transition-all duration-300'>
                    {getTestimonial(0).name}
                  </div>
                  <div className='flex items-center mt-1 md:mt-2 transition-all duration-300'>
                    {[...Array(5)].map((_, i) => (
                      <StarIcon
                        key={i}
                        className='w-3 h-3 md:w-4 md:h-4 text-yellow-400 transition-all duration-200 hover:scale-110'
                      />
                    ))}
                  </div>
                </div>
              </div>
              {/* Smaller quote icon */}
              <img
                src='/images/inverted_comma.png'
                alt='inverted comma'
                className='w-8 h-6 md:w-18 md:h-12 opacity-20 transition-all duration-300'
              />
            </div>
          </div>

          {/* Right Card (90% offscreen right) */}
          <div
            className='absolute top-1/2 right-0 z-0 opacity-60 scale-90 bg-[#BBBCBC] rounded-xl md:rounded-3xl shadow-lg flex flex-col justify-center overflow-hidden transition-all duration-700 ease-in-out transform'
            style={{
              width: `${cardWidth}px`,
              height: `${cardHeight}px`,
              transform: `translate(${hiddenWidth}px, -50%)`,
              padding: window.innerWidth < 768 ? '16px 20px' : '32px 48px',
            }}
          >
            {/* Orange swirl SVG background */}
            <svg
              className='absolute inset-0 w-full h-full pointer-events-none opacity-30'
              viewBox='0 0 900 400'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
            >
              <path
                d='M100 400C200 120 700 120 800 400'
                stroke='#FFB347'
                strokeWidth='2'
                fill='none'
              />
              <path
                d='M200 400C300 180 600 180 700 400'
                stroke='#FFB347'
                strokeWidth='2'
                fill='none'
              />
              <path
                d='M300 400C350 240 550 240 600 400'
                stroke='#FFB347'
                strokeWidth='2'
                fill='none'
              />
            </svg>
            <img
              src='/images/inverted_comma.png'
              alt='inverted comma'
              className='absolute w-12 h-12 md:w-20 md:h-20 opacity-40 pointer-events-none right-4 top-4 md:right-10 md:top-10 z-10'
            />
            <blockquote className='text-sm md:text-xl lg:text-2xl text-white text-center font-light leading-relaxed truncate relative z-10 transition-all duration-500'>
              {getTestimonial(1).text}
            </blockquote>
            {/* Bottom Row: Avatar, Name, Stars */}
            <div className='flex items-center space-x-2 md:space-x-4 mt-4 md:mt-6 z-10 justify-center transition-all duration-500'>
              <img
                src={getTestimonial(1).image}
                alt={getTestimonial(1).name}
                className='w-8 h-8 md:w-14 md:h-14 rounded-full object-cover border border-white opacity-80'
              />
              <div>
                <div className='text-sm md:text-lg font-semibold text-[#F5E6E2] opacity-80'>
                  {getTestimonial(1).name}
                </div>
                <div className='flex items-center mt-1 md:mt-2'>
                  {[...Array(5)].map((_, i) => (
                    <StarIcon
                      key={i}
                      className='w-3 h-3 md:w-4 md:h-4 text-yellow-400 opacity-80'
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Testimonials
