import React from 'react';

const ExpertiseSection = () => (
  <section className="w-full bg-[#232323] py-16">
    <div className="container-custom flex flex-col md:flex-row items-center justify-between gap-12 px-6">
      {/* Left: Overlapping Images (screenshot style) */}
      <div className="relative flex-1 flex justify-center items-center min-w-[260px] min-h-[260px]">
        {/* Large image, bottom left */}
        <img
          src="/images/your-large-image.jpg"
          alt="Large"
          className="rounded-2xl border-8 border-white shadow-xl w-72 h-72 object-cover absolute left-0 bottom-0 z-10"
        />
        {/* Small image, top right */}
        <img
          src="/images/your-small-image.jpg"
          alt="Small"
          className="rounded-2xl border-8 border-white shadow-xl w-40 h-40 object-cover absolute right-0 -top-10 z-20"
        />
      </div>
      {/* Right: Text and CTA */}
      <div className="flex-1 flex flex-col items-start">
        <h2 className="text-white text-4xl md:text-6xl font-bold leading-tight mb-6">
          Leverage our expertise to restore your property to its original condition
        </h2>
        <p className="text-gray-300 text-lg mb-8">
          With certified professionals and eco-friendly solutions, we bring safety and cleanliness back to your home or business. Trust us to handle your restoration needs with care and expertise.
        </p>
        <button className="bg-[#00A826] hover:bg-green-700 text-white font-semibold text-lg px-8 py-4 rounded-lg flex items-center gap-2 transition">
          Contact us Today
          <span aria-hidden="true">↗</span>
        </button>
      </div>
    </div>
  </section>
);

export default ExpertiseSection; 