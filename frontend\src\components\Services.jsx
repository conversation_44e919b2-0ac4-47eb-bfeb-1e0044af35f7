import React from 'react'
import { CheckIcon } from '@heroicons/react/24/outline'

const services = [
  {
    image: '/images/mold.png',
    title: 'Mold Remediation',
    description:
      'Mold can harm your health and damage your property. Our certified team uses advanced techniques to safely and effectively remove mold, including:',
    features: [
      'Thorough inspection and testing.',
      'Safe containment and removal.',
      'Air quality testing to ensure your space is mold-free.',
      'Prevention tips to stop mold from returning.',
    ],
  },
  {
    image: '/images/flod.png',
    title: 'Flood Damage Restoration',
    description:
      "Flooding can cause extensive damage, but we're here to restore your property quickly and efficiently. Our flood restoration services include:",
    features: [
      'Rapid water extraction to minimize damage.',
      'Drying and dehumidification to prevent mold growth.',
      'Sanitization and repairs to bring your property back to life.',
    ],
  },
  {
    image: '/images/restoration.png',
    title: 'Equipment Rentals',
    description:
      'Need professional-grade equipment for your clean-up or restoration project? We offer a range of equipment rentals, including:',
    features: [
      'High-capacity dehumidifiers.',
      'Industrial air movers and fans.',
      'Water extraction units.',
    ],
  },
]

const Services = () => {
  return (
    <section id='services' className='py-16 md:py-20 bg-white'>
      <div className='container-custom'>
        {/* Services Grid */}
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8'>
          {services.map((service) => (
            <div key={service.title} className='services-card'>
              {/* Icon at the top */}
              <img
                src={service.image}
                alt={`${service.title} icon`}
                className='services-icon'
              />

              {/* Content below the icon */}
              <div className='services-content'>
                <h3 className='services-title'>{service.title}</h3>
                <p className='services-description'>{service.description}</p>

                {/* Features list */}
                <div className='services-features'>
                  <ul className='space-y-2'>
                    {service.features.map((feature, index) => (
                      <li
                        key={`${service.title}-feature-${index}`}
                        className='services-feature-item'
                      >
                        <CheckIcon className='services-feature-icon' />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Services
