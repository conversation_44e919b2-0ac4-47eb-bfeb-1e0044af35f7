import React, { useRef, useEffect } from 'react'

const brands = [
  '/images/qetrwtety1.png',
  '/images/qetrwtety1.png',
  '/images/qetrwtety1.png',
  '/images/qetrwtety1.png',
]

const SLIDE_SPEED = 40 // px per second - reduced for smoother animation

const Brands = () => {
  const rowRef = useRef(null)
  const animRef = useRef()
  const offsetRef = useRef(0)

  useEffect(() => {
    let lastTime = performance.now()
    const row = rowRef.current
    if (!row) return

    // Wait for images to load and get accurate width
    const checkAndStart = () => {
      const logoWidth = row.scrollWidth / 3 // since we render three times now
      if (logoWidth === 0) {
        setTimeout(checkAndStart, 100)
        return
      }

      function animate(now) {
        const dt = (now - lastTime) / 1000
        lastTime = now

        // Adjust speed based on screen size for consistent visual experience
        const screenWidth = window.innerWidth
        const speedMultiplier = screenWidth < 768 ? 0.7 : 1 // Slower on mobile
        const adjustedSpeed = SLIDE_SPEED * speedMultiplier

        offsetRef.current -= adjustedSpeed * dt

        // Smooth reset without stopping - when we've moved one full set, reset seamlessly
        if (offsetRef.current <= -logoWidth) {
          offsetRef.current = 0
        }

        row.style.transform = `translateX(${offsetRef.current}px)`
        animRef.current = requestAnimationFrame(animate)
      }

      animRef.current = requestAnimationFrame(animate)
    }

    checkAndStart()
    return () => cancelAnimationFrame(animRef.current)
  }, [])

  return (
    <section className='w-full h-[180px] md:h-[200px] bg-[#009D22] container-custom flex flex-col justify-center items-center'>
      <div className='container-custom flex flex-col md:flex-row items-center justify-start md:justify-between px-4 gap-6 md:gap-8'>
        <h2 className='text-white font-bold text-xl md:text-2xl mb-4 md:mb-0 whitespace-nowrap'>
          Brands That Trust Us:
        </h2>
        <div
          className='overflow-hidden w-full md:w-auto flex items-center'
          style={{ maxWidth: '100%' }}
        >
          <div
            ref={rowRef}
            className='flex flex-row gap-8 md:gap-12 items-center'
            style={{ willChange: 'transform' }}
          >
            {/* Render multiple sets for seamless infinite scroll */}
            {[...brands, ...brands, ...brands].map((src, idx) => (
              <img
                key={idx}
                src={src}
                alt='Brand Logo'
                className='h-14 md:h-16 w-auto object-contain flex-shrink-0 transition-all duration-300'
                draggable={false}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default Brands
