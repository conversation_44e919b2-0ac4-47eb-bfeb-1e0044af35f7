import React from 'react'
import ServicePageTemplate from '../components/ServicePageTemplate'

const OdourRemoval = () => {
  const pageData = {
    heroTitle: "Odour Removal and Deodorize",
    heroSubtitle: "Complete odour elimination and air purification services",
    heroImages: [
      {
        src: "/images/odour-hero-1.jpg",
        alt: "Professional odour removal service"
      },
      {
        src: "/images/odour-hero-2.jpg", 
        alt: "Advanced deodorization equipment"
      },
      {
        src: "/images/odour-hero-3.jpg",
        alt: "Fresh, clean indoor environment"
      }
    ],
    serviceTitle: "Odour Removal and Deodorize",
    serviceDescription: "Persistent odours can make your space uncomfortable and unhealthy. Our professional odour removal specialists use advanced techniques and equipment to identify odour sources and completely eliminate them, not just mask them. We restore fresh, clean air to your property.",
    serviceFeatures: [
      "Complete odour source identification",
      "Advanced ozone treatment systems",
      "Thermal fogging and hydroxyl generation", 
      "HVAC system cleaning and deodorization",
      "Carpet and upholstery deodorization",
      "Structural deodorization treatments",
      "Air duct cleaning and sanitization",
      "Long-term odour prevention strategies"
    ],
    focusAreas: {
      residential: [
        "Pet odour elimination",
        "Smoke and fire damage odours",
        "Cooking and food odours",
        "Basement and musty odours",
        "Sewage and water damage odours",
        "Chemical and paint odours",
        "Garbage and decomposition odours",
        "General household odour control"
      ],
      commercial: [
        "Restaurant and kitchen odours",
        "Office building air quality",
        "Retail space deodorization",
        "Healthcare facility odour control",
        "Hotel and hospitality services",
        "Automotive dealership services",
        "Industrial facility deodorization",
        "Property management solutions"
      ]
    },
    commonSigns: {
      title: "Signs you need professional odour removal:",
      signs: [
        "Persistent odours that won't go away",
        "Odours that return after cleaning",
        "Strong chemical or smoke smells",
        "Pet or animal odours throughout the space",
        "Musty or mildew odours",
        "Complaints from occupants or visitors"
      ]
    },
    additionalImages: [
      {
        src: "/images/ozone-treatment.jpg",
        alt: "Ozone treatment equipment"
      },
      {
        src: "/images/thermal-fogging.jpg", 
        alt: "Thermal fogging process"
      },
      {
        src: "/images/duct-cleaning.jpg",
        alt: "HVAC duct cleaning"
      },
      {
        src: "/images/fresh-air.jpg",
        alt: "Clean, fresh indoor air"
      }
    ]
  }

  return <ServicePageTemplate {...pageData} />
}

export default OdourRemoval
