import { Routes, Route } from 'react-router-dom'
import Header from './components/Header'
import Footer from './components/Footer'
import Home from './pages/Home'
import MoldRemediation from './pages/MoldRemediation'
import WaterFloodRestoration from './pages/WaterFloodRestoration'
import MoldInspectionTesting from './pages/MoldInspectionTesting'
import NewConstructionMold from './pages/NewConstructionMold'
import EquipmentRentals from './pages/EquipmentRentals'
import OdourRemoval from './pages/OdourRemoval'
import IndoorAirQuality from './pages/IndoorAirQuality'

function App() {
  return (
    <main className='min-h-screen bg-gray-900 text-white'>
      <Header />
      <Routes>
        <Route path='/' element={<Home />} />
        <Route
          path='/services/mold-remediation'
          element={<MoldRemediation />}
        />
        <Route
          path='/services/water-flood-restoration'
          element={<WaterFloodRestoration />}
        />
        <Route
          path='/services/mold-inspection-testing'
          element={<MoldInspectionTesting />}
        />
        <Route
          path='/services/new-construction-mold'
          element={<NewConstructionMold />}
        />
        <Route
          path='/services/equipment-rentals'
          element={<EquipmentRentals />}
        />
        <Route path='/services/odour-removal' element={<OdourRemoval />} />
        <Route
          path='/services/indoor-air-quality'
          element={<IndoorAirQuality />}
        />
      </Routes>
      <Footer />
    </main>
  )
}

export default App
