import React from 'react'
import ServicePageTemplate from '../components/ServicePageTemplate'

const EquipmentRentals = () => {
  const pageData = {
    heroTitle: "Drying, Remediation and Restoration Equipment Rentals",
    heroSubtitle: "Professional-grade equipment for your restoration projects",
    heroImages: [
      {
        src: "/images/equipment-hero-1.jpg",
        alt: "Professional restoration equipment"
      },
      {
        src: "/images/equipment-hero-2.jpg", 
        alt: "Industrial drying equipment"
      },
      {
        src: "/images/equipment-hero-3.jpg",
        alt: "Air filtration and purification systems"
      }
    ],
    serviceTitle: "Equipment Rentals",
    serviceDescription: "Access professional-grade restoration equipment for your DIY projects or professional restoration work. Our comprehensive inventory includes the latest technology in drying, air filtration, and remediation equipment, available for daily, weekly, or monthly rentals.",
    serviceFeatures: [
      "High-capacity commercial dehumidifiers",
      "Industrial air movers and fans",
      "Water extraction and pump equipment", 
      "HEPA air filtration systems",
      "Moisture detection instruments",
      "Negative air machines",
      "Specialty drying equipment",
      "24/7 equipment support and maintenance"
    ],
    focusAreas: {
      residential: [
        "Basement flooding cleanup",
        "DIY water damage restoration",
        "Home renovation projects",
        "Preventive moisture control",
        "Seasonal humidity management",
        "Emergency water removal",
        "Air quality improvement",
        "Small-scale restoration projects"
      ],
      commercial: [
        "Large-scale water damage projects",
        "Commercial property management",
        "Contractor equipment needs",
        "Industrial facility maintenance",
        "Emergency response operations",
        "Multi-unit property restoration",
        "Construction site drying",
        "Professional restoration companies"
      ]
    },
    commonSigns: {
      title: "When you might need equipment rentals:",
      signs: [
        "Water damage requiring immediate action",
        "High humidity levels in your space",
        "Need for professional-grade drying",
        "Large area requiring air filtration",
        "Cost-effective alternative to full service",
        "DIY restoration project requirements"
      ]
    },
    additionalImages: [
      {
        src: "/images/dehumidifiers.jpg",
        alt: "Commercial dehumidifiers"
      },
      {
        src: "/images/air-movers.jpg", 
        alt: "Industrial air movers"
      },
      {
        src: "/images/extraction-equipment.jpg",
        alt: "Water extraction equipment"
      },
      {
        src: "/images/air-filtration.jpg",
        alt: "HEPA air filtration systems"
      }
    ]
  }

  return <ServicePageTemplate {...pageData} />
}

export default EquipmentRentals
